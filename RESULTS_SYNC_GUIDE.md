# 📊 结果文件同步指南

## 🔍 问题说明

在使用Figma组件恢复工具时，截图和比较结果默认会保存到工具的默认项目路径：
```
/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/
```

但是当前项目位于：
```
/Users/<USER>/Documents/study/11111/
```

这导致ComparisonReport.vue组件无法找到生成的图片文件。

## ✅ 解决方案

### 1. 自动同步脚本

使用提供的 `sync-results.sh` 脚本自动同步结果文件：

```bash
# 给脚本添加执行权限
chmod +x sync-results.sh

# 运行同步脚本
./sync-results.sh
```

### 2. 手动同步

如果需要手动同步特定组件的结果：

```bash
# 创建目标目录
mkdir -p ./src/results/ComponentName
mkdir -p ./figma-restoration-mcp-vue-tools/public/results/ComponentName

# 复制文件
cp /Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/ComponentName/* \
   ./src/results/ComponentName/

cp /Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/ComponentName/* \
   ./figma-restoration-mcp-vue-tools/public/results/ComponentName/
```

## 📁 文件结构

同步后的文件结构：

```
项目根目录/
├── src/results/
│   └── ComponentName/
│       ├── expected.png          # Figma设计图
│       ├── actual.png            # 实际截图
│       ├── diff.png              # 差异图
│       ├── figma-analysis-report.json
│       ├── figma-analysis-report.md
│       └── region-analysis.json
├── figma-restoration-mcp-vue-tools/public/results/
│   └── ComponentName/
│       └── (相同的文件)
└── sync-results.sh               # 同步脚本
```

## 🔧 ComparisonReport.vue 修复

已修复的图片加载问题：

1. **使用new URL()语法** - 支持Vite动态导入
2. **多路径尝试机制** - 自动尝试多个可能的路径
3. **Fallback机制** - 图片不存在时显示占位符
4. **错误处理** - 优雅处理加载失败的情况

## 🎯 使用建议

1. **每次生成新的比较结果后**，运行 `./sync-results.sh` 同步文件
2. **在CI/CD环境中**，可以将同步脚本加入到构建流程
3. **开发时**，确保图片文件在 `src/results/` 或 `public/results/` 目录下

## 🚀 自动化建议

可以考虑在以下时机自动运行同步脚本：

1. 在截图工具完成后
2. 在比较测试完成后  
3. 在开发服务器启动前
4. 作为npm script的一部分

```json
{
  "scripts": {
    "sync-results": "./sync-results.sh",
    "dev": "./sync-results.sh && npm run serve"
  }
}
```

## 📝 注意事项

- 确保源路径存在且有读取权限
- 同步脚本会覆盖目标目录中的同名文件
- 建议定期清理旧的结果文件以节省空间
