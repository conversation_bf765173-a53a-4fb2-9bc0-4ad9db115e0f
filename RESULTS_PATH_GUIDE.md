# 📊 结果文件路径指南

## 🎯 自动路径检测（推荐）

工具现在支持自动检测项目路径，无需手动指定：

### 📸 截图工具（自动检测）
```javascript
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName"
  // projectPath 会自动检测，无需指定
})
```

### 🔍 比较工具（自动检测）
```javascript
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName"
  // projectPath 会自动检测，无需指定
})
```

## ✅ 手动指定路径（备用方式）

如果自动检测失败，仍可手动指定：

### 📸 截图工具
```javascript
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName",
  projectPath: "/Users/<USER>/Documents/study/11111", // 手动指定项目路径
  // ... 其他参数
})
```

### 🔍 比较工具
```javascript
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName",
  projectPath: "/Users/<USER>/Documents/study/11111", // 手动指定项目路径
  // ... 其他参数
})
```

## 📁 文件结构

生成的文件会保存到组件同级的results目录：

```
figma-restoration-mcp-vue-tools/src/components/ComponentName/
├── index.vue                     # Vue组件
├── images/                       # 组件图片资源
│   └── qr-code.png
├── metadata.json                 # 组件元数据
└── results/                      # 比较测试结果（新位置）
    ├── expected.png              # Figma设计图
    ├── actual.png                # 实际截图
    ├── diff.png                  # 差异图
    ├── figma-analysis-report.json
    ├── figma-analysis-report.md
    └── region-analysis.json
```

## 🌐 Web访问

ComparisonReport.vue组件通过以下路径访问图片：
- `http://localhost:1932/src/components/ComponentName/results/expected.png`
- `http://localhost:1932/src/components/ComponentName/results/actual.png`
- `http://localhost:1932/src/components/ComponentName/results/diff.png`

或者通过向后兼容的路径：
- `http://localhost:1932/results/ComponentName/expected.png`
- `http://localhost:1932/results/ComponentName/actual.png`
- `http://localhost:1932/results/ComponentName/diff.png`

## 🔧 工作流程

1. **生成截图**：指定正确的projectPath
2. **下载Figma设计图**：到同一目录
3. **运行比较测试**：指定正确的projectPath
4. **复制到public目录**：供Web访问
5. **查看比较报告**：`http://localhost:1932/report/ComponentName`

## 📝 示例命令

```bash
# 1. 生成截图（自动使用正确路径）
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  projectPath: "/Users/<USER>/Documents/study/11111"
})

# 2. 下载Figma设计图
download_figma_images_figma-context({
  localPath: "/Users/<USER>/Documents/study/11111/mcp-vue-tools/results/EnterpriseInfo"
})

# 3. 运行比较测试
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  projectPath: "/Users/<USER>/Documents/study/11111"
})

# 4. 复制到public目录
cp ./mcp-vue-tools/results/EnterpriseInfo/* \
   ./figma-restoration-mcp-vue-tools/public/results/EnterpriseInfo/
```

## 🎯 优势

- ✅ **无需文件复制** - 直接生成到正确位置
- ✅ **路径一致性** - 所有工具使用相同的项目路径
- ✅ **简化的代码** - ComparisonReport.vue逻辑更简洁
- ✅ **更好的性能** - 避免不必要的文件操作
