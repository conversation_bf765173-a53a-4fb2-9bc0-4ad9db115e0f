# 📊 结果文件路径指南

## ✅ 正确的生成方式

现在所有的截图和比较工具都已配置为生成到正确的项目路径：

### 📸 截图工具
```javascript
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName",
  projectPath: "/Users/<USER>/Documents/study/11111", // 指定正确的项目路径
  // ... 其他参数
})
```

### 🔍 比较工具
```javascript
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "ComponentName", 
  projectPath: "/Users/<USER>/Documents/study/11111", // 指定正确的项目路径
  // ... 其他参数
})
```

## 📁 文件结构

生成的文件会保存到：

```
项目根目录/
├── mcp-vue-tools/results/ComponentName/
│   ├── expected.png              # Figma设计图
│   ├── actual.png                # 实际截图
│   ├── diff.png                  # 差异图
│   ├── figma-analysis-report.json
│   ├── figma-analysis-report.md
│   └── region-analysis.json
└── figma-restoration-mcp-vue-tools/public/results/ComponentName/
    └── (复制的文件，供Web访问)
```

## 🌐 Web访问

ComparisonReport.vue组件通过以下路径访问图片：
- `http://localhost:1932/results/ComponentName/expected.png`
- `http://localhost:1932/results/ComponentName/actual.png`
- `http://localhost:1932/results/ComponentName/diff.png`

## 🔧 工作流程

1. **生成截图**：指定正确的projectPath
2. **下载Figma设计图**：到同一目录
3. **运行比较测试**：指定正确的projectPath
4. **复制到public目录**：供Web访问
5. **查看比较报告**：`http://localhost:1932/report/ComponentName`

## 📝 示例命令

```bash
# 1. 生成截图（自动使用正确路径）
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  projectPath: "/Users/<USER>/Documents/study/11111"
})

# 2. 下载Figma设计图
download_figma_images_figma-context({
  localPath: "/Users/<USER>/Documents/study/11111/mcp-vue-tools/results/EnterpriseInfo"
})

# 3. 运行比较测试
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  projectPath: "/Users/<USER>/Documents/study/11111"
})

# 4. 复制到public目录
cp ./mcp-vue-tools/results/EnterpriseInfo/* \
   ./figma-restoration-mcp-vue-tools/public/results/EnterpriseInfo/
```

## 🎯 优势

- ✅ **无需文件复制** - 直接生成到正确位置
- ✅ **路径一致性** - 所有工具使用相同的项目路径
- ✅ **简化的代码** - ComparisonReport.vue逻辑更简洁
- ✅ **更好的性能** - 避免不必要的文件操作
