#!/bin/bash

# 同步结果文件脚本
# 将工具生成的图片和报告从默认路径同步到当前项目

SOURCE_DIR="/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results"
TARGET_SRC_DIR="./src/results"
TARGET_PUBLIC_DIR="./figma-restoration-mcp-vue-tools/public/results"

echo "🔄 开始同步结果文件..."

# 检查源目录是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ 源目录不存在: $SOURCE_DIR"
    exit 1
fi

# 创建目标目录
mkdir -p "$TARGET_SRC_DIR"
mkdir -p "$TARGET_PUBLIC_DIR"

# 同步所有组件的结果
for component_dir in "$SOURCE_DIR"/*; do
    if [ -d "$component_dir" ]; then
        component_name=$(basename "$component_dir")
        echo "📁 同步组件: $component_name"
        
        # 创建目标目录
        mkdir -p "$TARGET_SRC_DIR/$component_name"
        mkdir -p "$TARGET_PUBLIC_DIR/$component_name"
        
        # 复制所有文件
        if [ "$(ls -A "$component_dir")" ]; then
            cp "$component_dir"/* "$TARGET_SRC_DIR/$component_name/" 2>/dev/null
            cp "$component_dir"/* "$TARGET_PUBLIC_DIR/$component_name/" 2>/dev/null
            echo "✅ 已同步 $component_name"
        else
            echo "⚠️  $component_name 目录为空"
        fi
    fi
done

echo "🎉 同步完成！"
echo "📊 结果文件位置:"
echo "   - src/results/"
echo "   - figma-restoration-mcp-vue-tools/public/results/"
