# 🎯 自动路径检测功能指南

## ✅ 功能概述

MCP工具现在支持自动检测项目路径，无需手动指定`projectPath`参数。工具会按以下优先级自动检测：

1. **环境变量** - `PROJECT_ROOT`
2. **配置文件** - `mcp-project-config.json`
3. **MCP工具位置推导** - 从工具安装位置推导项目根目录
4. **项目标识文件** - 查找`package.json`、`.git`等文件
5. **当前工作目录** - 作为最后的fallback

## 🔧 使用方法

### 方法1: 环境变量（推荐）

```bash
# 设置环境变量
export PROJECT_ROOT=/path/to/your/project

# 然后使用工具时不需要指定projectPath
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "YourComponent"
  // projectPath 会自动检测
})
```

### 方法2: 配置文件

在项目根目录创建 `mcp-project-config.json`：

```json
{
  "projectRoot": "/Users/<USER>/Documents/study/11111",
  "description": "MCP工具项目配置文件",
  "version": "1.0.0"
}
```

### 方法3: 自动推导（默认）

如果工具安装在项目的子目录中（如`figma-restoration-mcp-vue-tools/`），工具会自动推导项目根目录。

## 📊 检测结果验证

可以通过以下命令验证自动检测结果：

```bash
node -e "
import('./figma-restoration-mcp-vue-tools/src/utils/path-config.js').then(module => {
  console.log('Auto-detected project path:', module.getAutoDetectedProjectPath());
}).catch(console.error);
"
```

## 🎯 工具使用示例

### 截图工具（自动检测路径）

```javascript
snapdom_screenshot_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  port: 1932,
  snapDOMOptions: {
    scale: 3,
    backgroundColor: "transparent"
  }
  // 不需要指定 projectPath，会自动检测
})
```

### 比较工具（自动检测路径）

```javascript
figma_compare_figma-restoration-mcp-vue-tools({
  componentName: "EnterpriseInfo",
  threshold: 0.1,
  generateReport: true
  // 不需要指定 projectPath，会自动检测
})
```

## 📁 文件结构

自动检测后，文件会生成到：

```
auto-detected-project-root/
├── mcp-vue-tools/results/ComponentName/
│   ├── expected.png
│   ├── actual.png
│   ├── diff.png
│   ├── figma-analysis-report.json
│   └── figma-analysis-report.md
└── figma-restoration-mcp-vue-tools/public/results/ComponentName/
    └── (复制的文件，供Web访问)
```

## 🔍 故障排除

### 如果自动检测失败

1. **检查环境变量**：
   ```bash
   echo $PROJECT_ROOT
   ```

2. **检查配置文件**：
   ```bash
   cat mcp-project-config.json
   ```

3. **手动指定路径**：
   ```javascript
   snapdom_screenshot_figma-restoration-mcp-vue-tools({
     componentName: "YourComponent",
     projectPath: "/absolute/path/to/your/project"
   })
   ```

### 常见问题

- **MCP工具缓存**：重启MCP服务器可能需要重新检测
- **权限问题**：确保工具有读取项目目录的权限
- **路径格式**：使用绝对路径，避免相对路径问题

## 🎉 优势

- ✅ **简化使用** - 无需手动指定路径
- ✅ **自动适配** - 适应不同的项目结构
- ✅ **多种检测方式** - 提供多个fallback选项
- ✅ **向后兼容** - 仍支持手动指定路径
- ✅ **团队友好** - 其他开发者使用更方便

## 📝 注意事项

- 环境变量优先级最高
- 配置文件应放在项目根目录
- 自动检测基于常见的项目标识文件
- 建议在团队中统一使用环境变量或配置文件方式
