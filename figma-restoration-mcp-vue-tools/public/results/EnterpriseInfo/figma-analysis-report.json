{"componentName": "EnterpriseInfo", "timestamp": "2025-07-18T13:52:03.801Z", "comparison": {"matchPercentage": 93.64630541871921, "diffPixels": 77388, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "paths": {"expected": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/EnterpriseInfo/expected.png", "actual": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/EnterpriseInfo/actual.png", "diff": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/EnterpriseInfo/diff.png"}, "regionAnalysis": {"summary": {"matchPercentage": 95.04, "totalDiffPixels": 60397, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "qualityLevel": {"level": "good", "emoji": "✅", "text": "优秀"}, "totalDifferenceRegions": 1}, "regions": [{"id": "region_1", "boundingBox": {"x": 0, "y": 0, "width": 750, "height": 1624}, "center": {"x": 375, "y": 812}, "area": 1218000, "relativePosition": {"x": 0.5, "y": 0.5}, "colorAnalysis": {"averageColorDiff": 0, "maxColorDiff": 0, "minColorDiff": 0}, "regionType": "large_area", "severity": "major", "semanticInfo": null}], "semanticAnalysis": {"message": "No Figma data available for semantic analysis"}, "recommendations": [{"priority": "high", "type": "layout_fixes", "description": "1 个大面积差异，可能是布局问题", "regions": ["region_1"]}], "timestamp": "2025-07-18T13:52:03.800Z"}, "qualityLevel": {"level": "good", "emoji": "✅", "text": "良好", "needsSelfReflective": true}}, "regionAnalysis": {"summary": {"matchPercentage": 95.04, "totalDiffPixels": 60397, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "qualityLevel": {"level": "good", "emoji": "✅", "text": "优秀"}, "totalDifferenceRegions": 1}, "regions": [{"id": "region_1", "boundingBox": {"x": 0, "y": 0, "width": 750, "height": 1624}, "center": {"x": 375, "y": 812}, "area": 1218000, "relativePosition": {"x": 0.5, "y": 0.5}, "colorAnalysis": {"averageColorDiff": 0, "maxColorDiff": 0, "minColorDiff": 0}, "regionType": "large_area", "severity": "major", "semanticInfo": null}], "semanticAnalysis": {"message": "No Figma data available for semantic analysis"}, "recommendations": [{"priority": "high", "type": "layout_fixes", "description": "1 个大面积差异，可能是布局问题", "regions": ["region_1"]}], "timestamp": "2025-07-18T13:52:03.800Z"}, "summary": {"status": {"level": "good", "emoji": "✅", "text": "良好", "needsSelfReflective": true}, "recommendation": "🔄 **触发Self-Reflective分析** - 还原度未达到98%标准，需要启动自动重新分析流程：\n1. 重新深度分析Figma JSON数据\n2. 验证素材完整性和位置精确性\n3. 执行针对性修复和迭代优化\n4. 目标：达到98%+还原度 重点关注：1 个大面积差异，可能是布局问题", "nextSteps": ["优先处理：1 个大面积差异，可能是布局问题", "🔄 **立即启动Self-Reflective分析流程**", "📊 重新获取和深度分析Figma JSON数据", "🎨 验证所有素材完整性和正确性", "📐 重新计算元素位置和布局精确性", "🔧 执行针对性修复和优化", "🎯 目标：达到98%+还原度"]}}