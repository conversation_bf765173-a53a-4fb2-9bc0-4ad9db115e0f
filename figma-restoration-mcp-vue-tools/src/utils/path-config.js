import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';

/**
 * 统一的路径配置工具
 * 自动检测当前工作目录，解决MCP环境中process.cwd()返回根目录的问题
 */

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 自动检测当前项目的根目录
 * 通过查找package.json、.git等标识文件来确定项目根目录
 * @param {string} startPath - 开始搜索的路径（默认为当前工作目录）
 * @returns {Promise<string>} 项目根目录的绝对路径
 */
export async function detectProjectRoot(startPath = process.cwd()) {
  const indicators = ['package.json', '.git', 'vue.config.js', 'vite.config.js', 'nuxt.config.js'];

  let currentPath = path.resolve(startPath);
  const rootPath = path.parse(currentPath).root;

  while (currentPath !== rootPath) {
    // 检查当前目录是否包含项目标识文件
    for (const indicator of indicators) {
      try {
        const indicatorPath = path.join(currentPath, indicator);
        await fs.access(indicatorPath);
        console.log(`🎯 Auto-detected project root: ${currentPath} (found ${indicator})`);
        return currentPath;
      } catch {
        // 文件不存在，继续检查下一个
      }
    }

    // 向上一级目录
    currentPath = path.dirname(currentPath);
  }

  // 如果没有找到，返回当前工作目录
  console.log(`⚠️  No project indicators found, using current directory: ${startPath}`);
  return startPath;
}

/**
 * 获取MCP工具的根目录路径
 * @param {string} customPath - 自定义路径（可选）
 * @returns {string} MCP工具根目录的绝对路径
 */
export function getMCPToolsPath(customPath = null) {
  if (customPath) {
    return path.resolve(customPath);
  }

  // 从当前文件位置推导出MCP工具根目录
  // 当前文件在 src/utils/，所以需要向上两级
  const mcpToolsPath = path.resolve(__dirname, '../../');

  return mcpToolsPath;
}

/**
 * 获取组件目录路径
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 组件目录的绝对路径
 */
export function getComponentPath(componentName, customPath = null) {
  const mcpToolsPath = getMCPToolsPath(customPath);
  return path.join(mcpToolsPath, 'src/components', componentName);
}

/**
 * 获取结果目录路径（组件同级的results目录）
 * @param {string} componentName - 组件名称
 * @param {string} customProjectPath - 自定义项目路径（可选）
 * @returns {Promise<string>} 结果目录的绝对路径
 */
export async function getResultsPath(componentName, customProjectPath = null) {
  let projectPath;

  if (customProjectPath) {
    projectPath = path.resolve(customProjectPath);
  } else {
    // 自动检测项目根目录
    projectPath = await detectProjectRoot();
  }

  // 返回组件同级的results目录
  const mcpToolsPath = getMCPToolsPath();
  return path.join(mcpToolsPath, 'src', 'components', componentName, 'results');
}

/**
 * 获取输出目录路径
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 输出目录的绝对路径
 */
export function getOutputPath(componentName, customPath = null) {
  const mcpToolsPath = getMCPToolsPath(customPath);
  return path.join(mcpToolsPath, 'output', componentName);
}

/**
 * 获取资源目录路径
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 资源目录的绝对路径
 */
export function getAssetsPath(customPath = null) {
  const mcpToolsPath = getMCPToolsPath(customPath);
  return path.join(mcpToolsPath, 'assets');
}

/**
 * 获取组件图片目录路径
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 组件图片目录的绝对路径
 */
export function getComponentImagesPath(componentName, customPath = null) {
  const componentPath = getComponentPath(componentName, customPath);
  return path.join(componentPath, 'images');
}

/**
 * 获取组件的预期图片路径（Figma原图）
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 预期图片的绝对路径
 */
export function getComponentExpectedImagePath(componentName, customPath = null) {
  const imagesPath = getComponentImagesPath(componentName, customPath);
  return path.join(imagesPath, 'expected.png');
}

/**
 * 获取组件的实际截图路径
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 实际截图的绝对路径
 */
export function getComponentActualImagePath(componentName, customPath = null) {
  const resultsPath = getResultsPath(componentName, customPath);
  return path.join(resultsPath, 'actual.png');
}

/**
 * 获取组件的差异图片路径
 * @param {string} componentName - 组件名称
 * @param {string} customPath - 自定义MCP工具路径（可选）
 * @returns {string} 差异图片的绝对路径
 */
export function getComponentDiffImagePath(componentName, customPath = null) {
  const resultsPath = getResultsPath(componentName, customPath);
  return path.join(resultsPath, 'diff.png');
}

/**
 * 获取Vue开发服务器的URL
 * @param {number} port - 端口号（默认1932）
 * @param {string} componentName - 组件名称（可选）
 * @returns {string} Vue开发服务器的URL
 */
export function getVueServerUrl(port = 1932, componentName = null) {
  const baseUrl = `http://localhost:${port}`;
  return componentName ? `${baseUrl}/component/${componentName}` : baseUrl;
}

/**
 * 验证路径是否存在
 * @param {string} targetPath - 要验证的路径
 * @returns {Promise<boolean>} 路径是否存在
 */
export async function pathExists(targetPath) {
  try {
    const fs = await import('fs/promises');
    await fs.access(targetPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 创建目录（如果不存在）
 * @param {string} targetPath - 要创建的目录路径
 * @returns {Promise<boolean>} 是否成功创建
 */
export async function ensureDirectory(targetPath) {
  try {
    const fs = await import('fs/promises');
    await fs.mkdir(targetPath, { recursive: true });
    return true;
  } catch (error) {
    console.error(`Failed to create directory: ${targetPath}`, error);
    return false;
  }
}

/**
 * 获取自动检测的项目路径（同步版本，用于工具默认值）
 * @returns {string} 项目路径
 */
export function getAutoDetectedProjectPath() {
  // 1. 优先从环境变量获取
  if (process.env.PROJECT_ROOT) {
    console.log(`🎯 Using PROJECT_ROOT env var: ${process.env.PROJECT_ROOT}`);
    return process.env.PROJECT_ROOT;
  }

  // 2. 尝试从MCP工具的相对位置推导
  const mcpToolsPath = getMCPToolsPath();
  const possibleProjectRoot = path.dirname(mcpToolsPath);

  // 3. 尝试读取项目配置文件
  try {
    const configPath = path.join(possibleProjectRoot, 'mcp-project-config.json');
    const configContent = require('fs').readFileSync(configPath, 'utf-8');
    const config = JSON.parse(configContent);
    if (config.projectRoot) {
      console.log(`🎯 Using project root from config file: ${config.projectRoot}`);
      return config.projectRoot;
    }
  } catch {
    // 配置文件不存在或无法读取，继续其他方法
  }

  // 4. 检查推导的路径是否包含项目标识文件
  const indicators = ['package.json', '.git', 'vue.config.js', 'vite.config.js'];
  for (const indicator of indicators) {
    try {
      const indicatorPath = path.join(possibleProjectRoot, indicator);
      require('fs').accessSync(indicatorPath);
      console.log(`🎯 Auto-detected project root from MCP tools location: ${possibleProjectRoot}`);
      return possibleProjectRoot;
    } catch {
      // 继续检查下一个
    }
  }

  // 5. 尝试从当前工作目录开始检测
  const cwd = process.cwd();
  let currentPath = cwd;
  const rootPath = path.parse(currentPath).root;

  while (currentPath !== rootPath) {
    for (const indicator of indicators) {
      try {
        const indicatorPath = path.join(currentPath, indicator);
        require('fs').accessSync(indicatorPath);
        console.log(`🎯 Auto-detected project root from cwd: ${currentPath}`);
        return currentPath;
      } catch {
        // 继续检查
      }
    }
    currentPath = path.dirname(currentPath);
  }

  // 6. 如果都没找到，使用推导的项目根目录作为fallback
  console.log(`⚠️  No project indicators found, using inferred path: ${possibleProjectRoot}`);
  return possibleProjectRoot;
}

/**
 * 获取默认的MCP工具配置
 * @returns {object} 默认配置对象
 */
export function getDefaultConfig() {
  return {
    mcpToolsPath: getMCPToolsPath(),
    projectPath: getAutoDetectedProjectPath(),
    vueServerPort: 1932,
    defaultViewport: { width: 1152, height: 772 },
    defaultScreenshotOptions: {
      omitBackground: true,
      deviceScaleFactor: 2
    },
    defaultWaitOptions: {
      waitUntil: 'networkidle2',
      timeout: 15000,
      additionalWait: 1000
    }
  };
}
