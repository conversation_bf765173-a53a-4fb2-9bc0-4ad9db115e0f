<template>
  <div class="enterprise-info">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-left">
        <span class="time">9:41</span>
      </div>
      <div class="status-right">
        <div class="signal-icons">
          <div class="cellular-signal"></div>
          <div class="wifi-signal"></div>
          <div class="battery"></div>
        </div>
      </div>
    </div>

    <!-- Title Bar -->
    <div class="title-bar">
      <div class="title-left">
        <button class="back-button" @click="goBack">
          <svg width="8" height="16" viewBox="0 0 8 16" fill="none">
            <path d="M7 1L1 8L7 15" stroke="#212121" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <span class="title-text">企业专属服务</span>
      </div>
      <button class="usage-guide" @click="showUsageGuide" style="opacity: 0;">使用说明</button>
    </div>

    <!-- Enterprise Info Card -->
    <div class="info-card">
      <div class="info-row">
        <span class="info-label">企业名称</span>
        <span class="info-value">上海合合信息</span>
      </div>
      <div class="info-row">
        <span class="info-label">企业ID</span>
        <span class="info-value">325345435</span>
      </div>
      <div class="info-row">
        <span class="info-label">团队成员</span>
        <span class="info-value">5/10</span>
      </div>
      <div class="info-row">
        <span class="info-label">到期时间</span>
        <span class="info-value">2026.12.30</span>
      </div>
    </div>

    <!-- Customer Service Card -->
    <div class="service-card">
      <div class="info-row">
        <span class="info-label">专属客服</span>
        <a href="tel:13333334525" class="service-phone">13333334525</a>
      </div>
    </div>

    <!-- QR Code Section -->
    <div class="qr-section">
      <h3 class="qr-title">扫码添加专属客户经理</h3>
      <div class="qr-code">
        <img src="./images/qr-code.png" alt="QR Code" />
      </div>
      <p class="qr-subtitle">专属企微，售后无忧</p>
      <p class="qr-instruction">截图保存到相册，使用微信扫描识别添加</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterpriseInfo',
  methods: {
    goBack() {
      // Handle back navigation
      this.$emit('back')
    },
    showUsageGuide() {
      // Handle usage guide
      this.$emit('usage-guide')
    }
  }
}
</script>

<style scoped>
.enterprise-info {
  width: 375px;
  height: 812px;
  background-color: #F7F7F7;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow: hidden;
}

/* Status Bar - 精确匹配Figma尺寸 */
.status-bar {
  width: 375px;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

.status-left {
  position: absolute;
  left: 21px;
  top: 12px;
  width: 54px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-left .time {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 1.1;
}

.status-right {
  position: absolute;
  right: 14.67px;
  top: 17.33px;
  display: flex;
  align-items: center;
  gap: 5.03px;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 5.03px;
}

.cellular-signal {
  width: 17px;
  height: 10.67px;
  background-color: #000000;
  opacity: 1;
}

.wifi-signal {
  width: 15.27px;
  height: 10.97px;
  background-color: #000000;
  opacity: 1;
}

.battery {
  width: 24.33px;
  height: 11.33px;
  border: 1px solid #000000;
  border-radius: 2.67px;
  opacity: 0.35;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -1.33px;
  top: 3.67px;
  width: 1.33px;
  height: 4px;
  background-color: #000000;
  opacity: 0.4;
  border-radius: 0 1px 1px 0;
}

/* Title Bar - 精确匹配Figma布局 */
.title-bar {
  width: 375px;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 10px 16px;
  box-sizing: border-box;
  position: relative;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.back-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: opacity 0.2s ease;
}

.back-button:hover {
  opacity: 0.7;
}

.back-button:active {
  opacity: 0.5;
  transform: scale(0.95);
}

.title-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #212121;
  line-height: 1.5;
  text-align: center;
  flex: 1;
}

.usage-guide {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #00B796;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  line-height: 1.43;
}

/* Info Cards - 精确匹配Figma尺寸和间距 */
.info-card, .service-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  width: 343px;
  margin: 0 auto 16px auto;
  padding: 0;
  box-sizing: border-box;
}

.info-card {
  margin-top: 16px;
  margin-bottom: 16px;
}

.service-card {
  margin-top: 0;
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  min-height: 56px;
  box-sizing: border-box;
}

.info-row:not(:last-child) {
  border-bottom: 1px solid #F5F5F5;
}

.info-label {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #212121;
  line-height: 1.5;
  text-align: left;
}

.info-value {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #9C9C9C;
  line-height: 1.5;
  text-align: right;
}

.service-phone {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #00B796;
  line-height: 1.5;
  text-align: right;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.service-phone:hover {
  opacity: 0.8;
}

.service-phone:active {
  opacity: 0.6;
}

/* QR Code Section - 精确匹配Figma设计 */
.qr-section {
  background-color: #FFFFFF;
  border-radius: 8px;
  width: 343px;
  height: 264px;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.qr-title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #212121;
  line-height: 1.43;
  margin: 0;
  width: 238px;
  height: 20px;
  position: absolute;
  top: 24px;
  left: 53px;
}

.qr-code {
  width: 140px;
  height: 140px;
  position: absolute;
  top: 48px;
  left: 102px;
}

.qr-code img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qr-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #9C9C9C;
  line-height: 1.33;
  margin: 0;
  width: 238px;
  height: 16px;
  position: absolute;
  top: 204px;
  left: 53px;
}

.qr-instruction {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #9C9C9C;
  line-height: 1.33;
  margin: 0;
  width: 238px;
  height: 16px;
  position: absolute;
  top: 224px;
  left: 53px;
}
</style>
