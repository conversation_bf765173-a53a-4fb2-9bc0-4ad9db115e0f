{"componentName": "DocumentMigration", "timestamp": "2025-07-18T17:05:38.970Z", "comparison": {"matchPercentage": 88.99499178981938, "diffPixels": 134041, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "paths": {"expected": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/DocumentMigration/expected.png", "actual": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/DocumentMigration/actual.png", "diff": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools/results/DocumentMigration/diff.png"}, "regionAnalysis": {"summary": {"matchPercentage": 90.89, "totalDiffPixels": 110961, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "qualityLevel": {"level": "fair", "emoji": "👍", "text": "良好"}, "totalDifferenceRegions": 1}, "regions": [{"id": "region_1", "boundingBox": {"x": 0, "y": 0, "width": 750, "height": 1624}, "center": {"x": 375, "y": 812}, "area": 1218000, "relativePosition": {"x": 0.5, "y": 0.5}, "colorAnalysis": {"averageColorDiff": 13.856406460551009, "maxColorDiff": 13.856406460551018, "minColorDiff": 13.856406460551018}, "regionType": "large_area", "severity": "major", "semanticInfo": null}], "semanticAnalysis": {"message": "No Figma data available for semantic analysis"}, "recommendations": [{"priority": "high", "type": "layout_fixes", "description": "1 个大面积差异，可能是布局问题", "regions": ["region_1"]}], "timestamp": "2025-07-18T17:05:38.967Z"}, "qualityLevel": {"level": "needs_improvement", "emoji": "⚠️", "text": "需要改进", "needsSelfReflective": true}}, "regionAnalysis": {"summary": {"matchPercentage": 90.89, "totalDiffPixels": 110961, "totalPixels": 1218000, "dimensions": {"width": 750, "height": 1624}, "qualityLevel": {"level": "fair", "emoji": "👍", "text": "良好"}, "totalDifferenceRegions": 1}, "regions": [{"id": "region_1", "boundingBox": {"x": 0, "y": 0, "width": 750, "height": 1624}, "center": {"x": 375, "y": 812}, "area": 1218000, "relativePosition": {"x": 0.5, "y": 0.5}, "colorAnalysis": {"averageColorDiff": 13.856406460551009, "maxColorDiff": 13.856406460551018, "minColorDiff": 13.856406460551018}, "regionType": "large_area", "severity": "major", "semanticInfo": null}], "semanticAnalysis": {"message": "No Figma data available for semantic analysis"}, "recommendations": [{"priority": "high", "type": "layout_fixes", "description": "1 个大面积差异，可能是布局问题", "regions": ["region_1"]}], "timestamp": "2025-07-18T17:05:38.967Z"}, "summary": {"status": {"level": "needs_improvement", "emoji": "⚠️", "text": "需要改进", "needsSelfReflective": true}, "recommendation": "🔄 **触发Self-Reflective分析** - 还原度未达到98%标准，需要启动自动重新分析流程：\n1. 重新深度分析Figma JSON数据\n2. 验证素材完整性和位置精确性\n3. 执行针对性修复和迭代优化\n4. 目标：达到98%+还原度 重点关注：1 个大面积差异，可能是布局问题", "nextSteps": ["优先处理：1 个大面积差异，可能是布局问题", "🔄 **立即启动Self-Reflective分析流程**", "📊 重新获取和深度分析Figma JSON数据", "🎨 验证所有素材完整性和正确性", "📐 重新计算元素位置和布局精确性", "🔧 执行针对性修复和优化", "🎯 目标：达到98%+还原度"]}}