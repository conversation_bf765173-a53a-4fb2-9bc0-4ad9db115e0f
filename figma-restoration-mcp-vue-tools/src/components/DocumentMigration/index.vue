<template>
  <div class="document-migration">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="time">9:41</span>
      </div>
      <div class="status-right">
        <div class="signal-icons">
          <div class="mobile-signal"></div>
          <div class="wifi-signal"></div>
          <div class="battery"></div>
        </div>
      </div>
    </div>

    <!-- 标题栏 -->
    <div class="navigation-bar">
      <div class="nav-left">
        <button class="back-button">
          <svg width="8" height="16" viewBox="0 0 8 16" fill="none">
            <path d="M7 1L1 8L7 15" stroke="#212121" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <span class="nav-title">新增迁移</span>
      </div>
      <div class="nav-right">
        <span class="nav-action" style="opacity: 0;">使用说明</span>
      </div>
    </div>

    <!-- 分隔线 - 对应Rectangle 3464646 -->
    <div class="separator">
      <div class="separator-bg"></div>
    </div>

    <!-- 空间选择区域 - 精确按Figma布局 -->
    <div class="space-selection">
      <!-- 标题文本: x:16, y:97 -->
      <div class="section-title-space">选择需要迁移的空间</div>

      <!-- Group 427319703: x:16, y:125, width:148, height:24 -->
      <div class="group-427319703" @click="toggleSpaceSelection">
        <!-- 文件夹图标: x:0, y:0, width:24, height:24 -->
        <div class="folder-icon-space">
          <img src="./images/folder-icon.png" alt="空间图标" />
        </div>
        <!-- 文本: x:32, y:0, width:96, height:24 -->
        <div class="space-name-text">上海合合信息</div>
        <!-- 选择框: x:132, y:4, width:16, height:16 -->
        <div class="space-checkbox-group">
          <div class="space-checkbox-bg"></div>
          <div class="space-checkbox-inner" v-if="selectedSpace"></div>
        </div>
      </div>
    </div>

    <!-- 文档选择区域 -->
    <div class="document-selection">
      <h3 class="section-title">选择需要迁移的文档</h3>

      <!-- 文档列表容器 - 对应Group 427319704 -->
      <div class="document-list-container">
        <!-- 白色背景 - 对应Rectangle 3464647 -->
        <div class="document-list-background"></div>

        <!-- 文档列表 - 完全按照Figma结构重构 -->
        <div class="document-list">
          <!-- 新建文件夹 1 (y:16) -->
          <div class="document-frame frame-427319249-1">
            <div class="frame-427319616">
              <div class="group-427319705">
                <div class="rectangle-3464360"></div>
                <div class="folder-icon-instance">
                  <img src="./images/folder-icon.png" alt="文件夹图标" />
                </div>
                <div class="document-title-text">新建文件夹</div>
              </div>
              <div class="checkbox-instance">
                <div class="checkbox-rect" @click="toggleDocumentSelection('folder-1')" :class="{ checked: selectedDocuments.includes('folder-1') }">
                  <div class="checkbox-inner" v-if="selectedDocuments.includes('folder-1')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 新建文件夹 2 (y:96) -->
          <div class="document-frame frame-427319249-2">
            <div class="frame-427319616">
              <div class="group-427319705">
                <div class="rectangle-3464360"></div>
                <div class="folder-icon-instance">
                  <img src="./images/folder-icon.png" alt="文件夹图标" />
                </div>
                <div class="document-title-text">新建文件夹</div>
              </div>
              <div class="checkbox-instance">
                <div class="checkbox-rect" @click="toggleDocumentSelection('folder-2')" :class="{ checked: selectedDocuments.includes('folder-2') }">
                  <div class="checkbox-inner" v-if="selectedDocuments.includes('folder-2')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 商业化合作协议 (y:176) -->
          <div class="document-frame frame-427319220">
            <div class="frame-427319542">
              <div class="document-thumbnail-instance">
                <img src="./images/word-document-thumbnail.png" alt="Word文档缩略图" />
                <div class="document-property-icon">
                  <!-- Word图标 -->
                </div>
              </div>
              <div class="group-427319219">
                <div class="document-title-long">商业化合作协议商业化合作议化...</div>
                <div class="frame-427319214">
                  <!-- 文档元信息 -->
                </div>
                <div class="frame-427319219">
                  <!-- 标签区域 -->
                </div>
              </div>
            </div>
            <div class="checkbox-instance">
              <div class="checkbox-rect" @click="toggleDocumentSelection('doc-1')" :class="{ checked: selectedDocuments.includes('doc-1') }">
                <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-1')"></div>
              </div>
            </div>
          </div>

          <!-- 扫描全能王文档 (y:256) -->
          <div class="document-frame frame-427319223">
            <div class="group-427319219-scan">
              <div class="frame-427319548">
                <div class="document-title-scan">扫描全能王 2023-09-05 09:20</div>
                <div class="frame-427319214">
                  <!-- 文档元信息 -->
                </div>
              </div>
              <div class="document-thumbnail-64">
                <img src="./images/word-document-bg.png" alt="Word文档背景" />
                <div class="document-property-word">
                  <!-- Word属性图标 -->
                </div>
              </div>
            </div>
            <div class="checkbox-instance">
              <div class="checkbox-rect" @click="toggleDocumentSelection('doc-2')" :class="{ checked: selectedDocuments.includes('doc-2') }">
                <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-2')"></div>
              </div>
            </div>
          </div>

          <!-- Excel文档1 (y:336) -->
          <div class="document-frame frame-427319222">
            <div class="frame-427319540">
              <div class="document-thumbnail-excel">
                <img src="./images/excel-document-bg.png" alt="Excel文档背景" />
                <div class="document-property-excel">
                  <!-- Excel属性图标 -->
                </div>
              </div>
              <div class="group-427319219-excel">
                <div class="frame-427319541">
                  <!-- Excel文档信息 -->
                </div>
              </div>
            </div>
            <div class="checkbox-instance">
              <div class="checkbox-rect" @click="toggleDocumentSelection('doc-3')" :class="{ checked: selectedDocuments.includes('doc-3') }">
                <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-3')"></div>
              </div>
            </div>
          </div>

          <!-- Excel文档2 (y:416) -->
          <div class="document-frame frame-427319224">
            <div class="frame-427319540">
              <div class="document-thumbnail-excel">
                <img src="./images/excel-document-bg.png" alt="Excel文档背景" />
                <div class="document-property-excel">
                  <!-- Excel属性图标 -->
                </div>
              </div>
              <div class="group-427319219-excel">
                <div class="frame-427319541">
                  <!-- Excel文档信息 -->
                </div>
              </div>
            </div>
            <div class="checkbox-instance">
              <div class="checkbox-rect" @click="toggleDocumentSelection('doc-4')" :class="{ checked: selectedDocuments.includes('doc-4') }">
                <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-4')"></div>
              </div>
            </div>
          </div>

          <!-- Excel文档3 (y:496) -->
          <div class="document-frame frame-427319225">
            <div class="frame-427319540">
              <div class="document-thumbnail-excel">
                <img src="./images/excel-document-bg.png" alt="Excel文档背景" />
                <div class="document-property-excel">
                  <!-- Excel属性图标 -->
                </div>
              </div>
              <div class="group-427319219-excel">
                <div class="frame-427319541">
                  <!-- Excel文档信息 -->
                </div>
              </div>
            </div>
            <div class="checkbox-instance">
              <div class="checkbox-rect" @click="toggleDocumentSelection('doc-5')" :class="{ checked: selectedDocuments.includes('doc-5') }">
                <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-5')"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部背景 - 对应Rectangle 34624577 -->
    <div class="bottom-background"></div>

    <!-- 确认按钮区域 -->
    <div class="action-area">
      <!-- 主按钮 -->
      <button class="confirm-button primary" @click="confirmMigration" :disabled="!canConfirm">确定迁移</button>
      <!-- 底部半透明按钮 -->
      <button class="confirm-button secondary">确定迁移</button>
    </div>

    <!-- Home Indicator -->
    <div class="home-indicator">
      <div class="indicator-bar"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DocumentMigration',
  data() {
    return {
      selectedSpace: true, // 默认选中空间
      selectedDocuments: ['folder-1', 'doc-1'] // 默认选中一些文档以匹配设计
    }
  },
  computed: {
    canConfirm() {
      return this.selectedSpace && this.selectedDocuments.length > 0
    }
  },
  methods: {
    goBack() {
      // 返回上一页
      console.log('返回上一页')
      this.$router.go(-1)
    },
    toggleSpaceSelection() {
      // 切换空间选择
      this.selectedSpace = !this.selectedSpace
      console.log('空间选择状态:', this.selectedSpace)
    },
    toggleDocumentSelection(documentId) {
      // 切换文档选择
      const index = this.selectedDocuments.indexOf(documentId)
      if (index > -1) {
        this.selectedDocuments.splice(index, 1)
      } else {
        this.selectedDocuments.push(documentId)
      }
      console.log('已选择文档:', this.selectedDocuments)
    },
    confirmMigration() {
      // 确认迁移
      if (this.canConfirm) {
        console.log('确认迁移', {
          space: this.selectedSpace,
          documents: this.selectedDocuments
        })
        // 这里可以调用API进行迁移
      }
    }
  }
}
</script>

<style scoped>
.document-migration {
  width: 375px;
  height: 812px;
  background: #F7F7F7;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 状态栏样式 */
.status-bar {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.time {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.mobile-signal {
  width: 17px;
  height: 10.67px;
  background: #000000;
  opacity: 1;
  border-radius: 1px;
}

.wifi-signal {
  width: 15.27px;
  height: 10.97px;
  background: #000000;
  opacity: 1;
  border-radius: 1px;
}

.battery {
  width: 24.33px;
  height: 11.33px;
  background: #000000;
  opacity: 1;
  border-radius: 2.67px;
}

/* 标题栏样式 */
.navigation-bar {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.back-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
  line-height: 24px;
  text-align: center;
  flex: 1;
}

.nav-action {
  font-size: 14px;
  font-weight: 500;
  color: #00B796;
  line-height: 20px;
}

/* 分隔线 */
.separator {
  height: 74px;
  background: #F7F7F7;
  flex-shrink: 0;
  position: relative;
}

.separator-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 74px;
  background: #F7F7F7;
}

.separator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #E5E5E5;
}

/* 空间选择区域 - 精确按Figma布局 */
.space-selection {
  position: relative;
  background: #F7F7F7;
  flex-shrink: 0;
  height: 74px; /* Rectangle 3464646: y:89, height:74 */
  width: 375px;
}

/* 标题文本: x:16, y:97 */
.section-title-space {
  position: absolute;
  top: 8px; /* 97 - 89 = 8px */
  left: 16px;
  width: 126px;
  height: 20px;
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #9C9C9C;
}

/* Group 427319703: x:16, y:125 */
.group-427319703 {
  position: absolute;
  top: 36px; /* 125 - 89 = 36px */
  left: 16px;
  width: 148px;
  height: 24px;
  cursor: pointer;
}

/* 文件夹图标: x:0, y:0, width:24, height:24 */
.folder-icon-space {
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
}

.folder-icon-space img {
  width: 100%;
  height: 100%;
}

/* 文本: x:32, y:0, width:96, height:24 */
.space-name-text {
  position: absolute;
  top: 0;
  left: 32px;
  width: 96px;
  height: 24px;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #383838;
}

/* 选择框: x:132, y:4, width:16, height:16 */
.space-checkbox-group {
  position: absolute;
  top: 4px;
  left: 132px;
  width: 16px;
  height: 16px;
}

.space-checkbox-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  background: #D9D9D9;
  border-radius: 0;
}

.space-checkbox-inner {
  position: absolute;
  top: 0.74px;
  left: 2.34px;
  width: 11.31px;
  height: 11.31px;
  background: #212121;
  border-radius: 0.8px;
}

.section-title {
  font-size: 14px;
  font-weight: 400;
  color: #9C9C9C;
  margin: 0 0 8px 0;
  line-height: 20px;
}

.space-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 24px;
}

.space-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.space-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.space-name {
  font-size: 16px;
  font-weight: 500;
  color: #383838;
  line-height: 24px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #D9D9D9;
  border-radius: 2.67px;
  position: relative;
  background: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox.checked {
  border-color: #212121;
}

.checkbox-inner {
  width: 11.31px;
  height: 11.31px;
  background: #212121;
  border-radius: 0.8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 文档选择区域 */
.document-selection {
  background: #F7F7F7; /* 外层背景保持F7F7F7 */
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  position: relative;
}

.document-list-container {
  position: relative;
  margin-top: 16px;
  height: 613px; /* 对应Figma中Group 427319704的高度 */
}

.document-list-background {
  position: absolute;
  top: 0;
  left: -16px; /* 扩展到父容器边缘 */
  right: -16px;
  bottom: 0;
  background: #FFFFFF;
  z-index: 0;
}

/* 文档列表 - 按Figma精确布局 */
.document-list {
  position: relative;
  z-index: 1;
  padding: 0;
  margin: 0;
}

/* 文档Frame基础样式 */
.document-frame {
  position: absolute;
  width: 359px;
  height: 64px;
}

/* 各个Frame的精确位置 */
.frame-427319249-1 {
  top: 16px;
  left: 16px;
}

.frame-427319249-2 {
  top: 96px;
  left: 16px;
}

.frame-427319220 {
  top: 176px;
  left: 16px;
}

.frame-427319223 {
  top: 256px;
  left: 16px;
}

.frame-427319222 {
  top: 336px;
  left: 16px;
}

.frame-427319224 {
  top: 416px;
  left: 16px;
}

.frame-427319225 {
  top: 496px;
  left: 16px;
}

/* Frame 427319616 样式 */
.frame-427319616 {
  display: flex;
  flex-direction: row;
  width: 359px;
  height: 64px;
}

/* Group 427319705 样式 */
.group-427319705 {
  position: relative;
  width: 311px;
  height: 62px;
}

/* Rectangle 3464360 背景 */
.rectangle-3464360 {
  position: absolute;
  top: 2px;
  left: 0;
  width: 311px;
  height: 60px;
  background: #F7F7F7;
  border-radius: 4px;
}

/* 文件夹图标 */
.folder-icon-instance {
  position: absolute;
  top: 10px;
  left: 8px;
  width: 40px;
  height: 40px;
}

.folder-icon-instance img {
  width: 100%;
  height: 100%;
}

/* 文档标题 */
.document-title-text {
  position: absolute;
  top: 8px;
  left: 56px;
  width: 80px;
  height: 24px;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #212121;
}

/* 选择框实例 */
.checkbox-instance {
  width: 48px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-rect {
  width: 16px;
  height: 16px;
  border: 1px solid #CCCCCC;
  border-radius: 2.67px;
  background: #FFFFFF;
  cursor: pointer;
  position: relative;
}

.checkbox-rect.checked {
  border-color: #212121;
}

.checkbox-rect .checkbox-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 11.31px;
  height: 11.31px;
  background: #212121;
  border-radius: 0.8px;
}

/* Frame 427319542 样式 (商业化合作协议) */
.frame-427319542 {
  display: flex;
  flex-direction: row;
  gap: 12px;
  width: 311px;
  height: 64px;
}

.document-thumbnail-instance {
  width: 64px;
  height: 64px;
  position: relative;
}

.document-thumbnail-instance img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.document-property-icon {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 16px;
  height: 16px;
}

.group-427319219 {
  width: 235px;
  height: 64px;
  position: relative;
}

.document-title-long {
  position: absolute;
  top: 0;
  left: 0;
  width: 235px;
  height: 24px;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #212121;
}

/* Frame 427319548 样式 (扫描全能王) */
.frame-427319548 {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: absolute;
  top: 10px;
  left: 76px;
  width: 235px;
}

.document-title-scan {
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #212121;
  width: 235px;
}

.group-427319219-scan {
  position: relative;
  width: 311px;
  height: 64px;
}

.document-thumbnail-64 {
  position: absolute;
  top: 0;
  left: 0;
  width: 64px;
  height: 64px;
}

.document-thumbnail-64 img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

/* Frame 427319540 样式 (Excel文档) */
.frame-427319540 {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  width: 311px;
  height: 64px;
}

.document-thumbnail-excel {
  width: 64px;
  height: 64px;
  position: relative;
}

.document-thumbnail-excel img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.group-427319219-excel {
  width: 235px;
  height: 44px;
}

.frame-427319541 {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  height: 100%;
}

/* 旧样式已删除，使用新的Figma精确布局 */

/* 确认按钮 */
.action-area {
  position: relative;
  height: 76px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.confirm-button {
  position: absolute;
  height: 44px;
  background: #19BCAA;
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  line-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-button.primary {
  left: 32px;
  top: 8px; /* 710 - 702 = 8px from action-area top */
  width: 311px;
}

.confirm-button.secondary {
  left: 16px;
  top: 16px; /* 718 - 702 = 16px from action-area top */
  width: 343px;
  opacity: 0.6;
  z-index: 1;
}

.confirm-button:hover {
  background: #16A896;
}

.confirm-button:active {
  background: #139D90;
}

.confirm-button:disabled {
  background: #CCCCCC;
  cursor: not-allowed;
}

.confirm-button:disabled:hover {
  background: #CCCCCC;
}

/* 底部背景 */
.bottom-background {
  position: absolute;
  bottom: 34px; /* Home Indicator高度 */
  left: 0;
  right: 0;
  height: 76px;
  background: #F7F7F7;
  z-index: 0;
}

/* Home Indicator */
.home-indicator {
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #F7F7F7;
  flex-shrink: 0;
}

.indicator-bar {
  width: 134px;
  height: 5px;
  background: #000000;
  border-radius: 2.5px;
  opacity: 0.3;
}
</style>
