<template>
  <div class="document-migration">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="time">9:41</span>
      </div>
      <div class="status-right">
        <div class="signal-icons">
          <div class="mobile-signal"></div>
          <div class="wifi-signal"></div>
          <div class="battery"></div>
        </div>
      </div>
    </div>

    <!-- 标题栏 -->
    <div class="navigation-bar">
      <div class="nav-left">
        <button class="back-button">
          <svg width="8" height="16" viewBox="0 0 8 16" fill="none">
            <path d="M7 1L1 8L7 15" stroke="#212121" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <span class="nav-title">新增迁移</span>
      </div>
      <div class="nav-right">
        <span class="nav-action" style="opacity: 0;">使用说明</span>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 空间选择区域 -->
    <div class="space-selection">
      <h3 class="section-title">选择需要迁移的空间</h3>
      <div class="space-item" @click="toggleSpaceSelection">
        <div class="space-info">
          <div class="space-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V9C21 7.89543 20.1046 7 19 7H5C3.89543 7 3 7.89543 3 7Z" fill="#1E2939"/>
              <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V9C21 7.89543 20.1046 7 19 7H5C3.89543 7 3 7.89543 3 7Z" fill="#25C4A4"/>
            </svg>
          </div>
          <span class="space-name">上海合合信息</span>
        </div>
        <div class="checkbox" :class="{ checked: selectedSpace }">
          <div class="checkbox-inner" v-if="selectedSpace"></div>
        </div>
      </div>
    </div>

    <!-- 文档选择区域 -->
    <div class="document-selection">
      <h3 class="section-title">选择需要迁移的文档</h3>
      
      <!-- 文档列表 -->
      <div class="document-list">
        <!-- 新建文件夹 -->
        <div class="document-item">
          <div class="document-content">
            <div class="document-thumbnail folder">
              <div class="folder-icon">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                  <rect x="6" y="7.5" width="27" height="21" rx="1.5" fill="#EBF1F6"/>
                  <rect x="8.5" y="9.5" width="26" height="21" rx="1.5" fill="#C3D9E1"/>
                  <rect x="8" y="11.5" width="28" height="21" rx="1.5" fill="#139D90"/>
                </svg>
              </div>
            </div>
            <div class="document-info">
              <h4 class="document-title">新建文件夹</h4>
              <div class="document-meta">
                <span class="document-date">2023-09-01 14:36</span>
                <div class="document-stats">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <rect x="2" y="1.5" width="8" height="9" rx="0.5" stroke="#9C9C9C"/>
                    <path d="M4.5 5H7.5" stroke="#9C9C9C" stroke-width="1"/>
                    <path d="M4.5 7H5.5" stroke="#9C9C9C" stroke-width="1"/>
                  </svg>
                  <span>0</span>
                </div>
              </div>
            </div>
          </div>
          <div class="item-checkbox" @click="toggleDocumentSelection('folder-1')" :class="{ checked: selectedDocuments.includes('folder-1') }">
            <div class="checkbox-inner" v-if="selectedDocuments.includes('folder-1')"></div>
          </div>
        </div>

        <!-- 文档项目1 -->
        <div class="document-item">
          <div class="document-content">
            <div class="document-thumbnail">
              <img src="/src/components/DocumentMigration/images/document-thumbnail-1.png" alt="文档缩略图" />
            </div>
            <div class="document-info">
              <h4 class="document-title">测试文档.docx</h4>
              <div class="document-meta">
                <span class="document-date">2023-09-01 14:36</span>
                <div class="document-stats">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <rect x="2" y="1.5" width="8" height="9" rx="0.5" stroke="#9C9C9C"/>
                    <path d="M4.5 5H7.5" stroke="#9C9C9C" stroke-width="1"/>
                    <path d="M4.5 7H5.5" stroke="#9C9C9C" stroke-width="1"/>
                  </svg>
                  <span>5</span>
                </div>
              </div>
              <div class="document-tags">
                <span class="tag syncing">正在同步</span>
                <span class="tag word">Word</span>
              </div>
            </div>
          </div>
          <div class="item-checkbox" @click="toggleDocumentSelection('doc-1')" :class="{ checked: selectedDocuments.includes('doc-1') }">
            <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-1')"></div>
          </div>
        </div>

        <!-- 文档项目2 -->
        <div class="document-item">
          <div class="document-content">
            <div class="document-thumbnail excel">
              <div class="excel-icon">
                <svg width="44" height="44" viewBox="0 0 44 44" fill="none">
                  <rect width="44" height="44" rx="10" fill="#EFF7FF"/>
                  <rect x="6.42" y="3.67" width="31.17" height="36.67" fill="#419BFF"/>
                  <rect x="10.08" y="22.37" width="23.83" height="7.33" fill="#1685FF"/>
                </svg>
              </div>
            </div>
            <div class="document-info">
              <h4 class="document-title">数据统计表.xlsx</h4>
              <div class="document-meta">
                <span class="document-date">2023-09-01 14:36</span>
                <div class="document-stats">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <rect x="2" y="1.5" width="8" height="9" rx="0.5" stroke="#9C9C9C"/>
                    <path d="M4.5 5H7.5" stroke="#9C9C9C" stroke-width="1"/>
                    <path d="M4.5 7H5.5" stroke="#9C9C9C" stroke-width="1"/>
                  </svg>
                  <span>12</span>
                </div>
              </div>
              <div class="document-tags">
                <span class="tag completed">同步完成</span>
                <span class="tag excel">Excel</span>
              </div>
            </div>
          </div>
          <div class="item-checkbox" @click="toggleDocumentSelection('doc-2')" :class="{ checked: selectedDocuments.includes('doc-2') }">
            <div class="checkbox-inner" v-if="selectedDocuments.includes('doc-2')"></div>
          </div>
        </div>

        <!-- 更多文档项... -->
      </div>
    </div>

    <!-- 确认按钮 -->
    <div class="action-area">
      <button class="confirm-button" @click="confirmMigration" :disabled="!canConfirm">确定迁移</button>
    </div>

    <!-- Home Indicator -->
    <div class="home-indicator">
      <div class="indicator-bar"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DocumentMigration',
  data() {
    return {
      selectedSpace: true, // 默认选中空间
      selectedDocuments: []
    }
  },
  computed: {
    canConfirm() {
      return this.selectedSpace && this.selectedDocuments.length > 0
    }
  },
  methods: {
    goBack() {
      // 返回上一页
      console.log('返回上一页')
      this.$router.go(-1)
    },
    toggleSpaceSelection() {
      // 切换空间选择
      this.selectedSpace = !this.selectedSpace
      console.log('空间选择状态:', this.selectedSpace)
    },
    toggleDocumentSelection(documentId) {
      // 切换文档选择
      const index = this.selectedDocuments.indexOf(documentId)
      if (index > -1) {
        this.selectedDocuments.splice(index, 1)
      } else {
        this.selectedDocuments.push(documentId)
      }
      console.log('已选择文档:', this.selectedDocuments)
    },
    confirmMigration() {
      // 确认迁移
      if (this.canConfirm) {
        console.log('确认迁移', {
          space: this.selectedSpace,
          documents: this.selectedDocuments
        })
        // 这里可以调用API进行迁移
      }
    }
  }
}
</script>

<style scoped>
.document-migration {
  width: 375px;
  height: 812px;
  background: #F7F7F7;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 状态栏样式 */
.status-bar {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.time {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.mobile-signal {
  width: 17px;
  height: 10.67px;
  background: #000000;
  opacity: 1;
  border-radius: 1px;
}

.wifi-signal {
  width: 15.27px;
  height: 10.97px;
  background: #000000;
  opacity: 1;
  border-radius: 1px;
}

.battery {
  width: 24.33px;
  height: 11.33px;
  background: #000000;
  opacity: 1;
  border-radius: 2.67px;
}

/* 标题栏样式 */
.navigation-bar {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.back-button {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
  line-height: 24px;
  text-align: center;
  flex: 1;
}

.nav-action {
  font-size: 14px;
  font-weight: 500;
  color: #00B796;
  line-height: 20px;
}

/* 分隔线 */
.separator {
  height: 74px;
  background: #F7F7F7;
  flex-shrink: 0;
  position: relative;
}

.separator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #E5E5E5;
}

/* 空间选择区域 */
.space-selection {
  padding: 16px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 400;
  color: #9C9C9C;
  margin: 0 0 8px 0;
  line-height: 20px;
}

.space-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 24px;
}

.space-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.space-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.space-name {
  font-size: 16px;
  font-weight: 500;
  color: #383838;
  line-height: 24px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #D9D9D9;
  border-radius: 2.67px;
  position: relative;
  background: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox.checked {
  border-color: #212121;
}

.checkbox-inner {
  width: 11.31px;
  height: 11.31px;
  background: #212121;
  border-radius: 0.8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 文档选择区域 */
.document-selection {
  background: #FFFFFF;
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.document-list {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: transparent;
  border-radius: 4px;
}

.document-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  padding: 16px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #F0F0F0;
}

.document-thumbnail {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.document-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.document-thumbnail.folder {
  background: #FFFFFF;
}

.document-thumbnail.excel {
  background: #EFF7FF;
}

.document-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.document-title {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
  margin: 0;
  line-height: 24px;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 6px;
}

.document-date {
  font-size: 12px;
  color: #9C9C9C;
  line-height: 16px;
}

.document-stats {
  display: flex;
  align-items: center;
  gap: 2px;
}

.document-stats span {
  font-size: 12px;
  color: #9C9C9C;
  line-height: 16px;
}

.document-tags {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.tag {
  padding: 0px 4px;
  font-size: 10px;
  line-height: 14px;
  border-radius: 2px;
  font-weight: 400;
}

.tag.syncing {
  background: #2C90FF;
  color: #FFFFFF;
}

.tag.completed {
  background: #27BE5F;
  color: #FFFFFF;
}

.tag.word {
  background: #7D7D7D;
  color: #FFFFFF;
}

.tag.excel {
  background: #7D7D7D;
  color: #FFFFFF;
}

.item-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #CCCCCC;
  border-radius: 2.67px;
  background: #FFFFFF;
  margin-left: 16px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.item-checkbox.checked {
  border-color: #212121;
}

.item-checkbox .checkbox-inner {
  width: 11.31px;
  height: 11.31px;
  background: #212121;
  border-radius: 0.8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 确认按钮 */
.action-area {
  padding: 16px 32px;
  background: #F7F7F7;
  flex-shrink: 0;
}

.confirm-button {
  width: 311px;
  height: 44px;
  background: #19BCAA;
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  line-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-button:hover {
  background: #16A896;
}

.confirm-button:active {
  background: #139D90;
}

.confirm-button:disabled {
  background: #CCCCCC;
  cursor: not-allowed;
}

.confirm-button:disabled:hover {
  background: #CCCCCC;
}

/* Home Indicator */
.home-indicator {
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #F7F7F7;
  flex-shrink: 0;
}

.indicator-bar {
  width: 134px;
  height: 5px;
  background: #000000;
  border-radius: 2.5px;
  opacity: 0.3;
}
</style>
